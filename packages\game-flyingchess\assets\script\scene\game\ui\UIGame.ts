import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import {
    _decorator,
    Component,
    error,
    EventTouch,
    instantiate,
    Label,
    Node,
    PageView,
    Prefab,
    Sprite,
    tween,
    UITransform,
    v3,
} from 'cc'
const { ccclass, property } = _decorator
import { audioEffect } from '@/core/business/hooks/Decorator'
import store from '@/core/business/store'
import {
    GameModelSinglePosMap,
    HeadNodeDefaultPosMap,
    Single,
} from '@/core/business/types/IGame'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import { GameInfoBroadcast } from '@/pb-generate/server/dixit/v1/game_pb'
import {
    Player,
    PlayerInfoMessage,
    PlayerSchema,
} from '@/pb-generate/server/dixit/v1/player_pb'
import { sleep } from '@/core/business/util/TimeUtils'
import {
    State as DeskState,
    State,
} from '@/pb-generate/server/dixit/v1/state_pb'
import { PlayerItem, PlayerState } from '../components/player/PlayerItem'
import { create } from '@bufbuild/protobuf'

import { BasePool } from '@/core/manager/gui/base/BasePool'

import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'
import { Board } from '../components/desk/Board'

type DeskProps = {}

type DeskData = {}

declare module '@/core/manager' {
    interface Manager {
        cardPool: BasePool
    }
}

@ccclass('UIGame')
export class UIGame extends BaseComponent<DeskProps, DeskData> {
    @property({ type: Board, tooltip: '棋盘' })
    board: Board = null!

    @property({ type: Node, tooltip: '观战节点' })
    watch_node: Node = null!

    @property({ type: Node, tooltip: '玩家根节点' })
    playerRootNode: Node = null!

    @property({ type: Prefab, tooltip: '玩家预制体' })
    players_prefab: Prefab = null!

    @property({ type: Prefab, tooltip: '卡牌预制体' })
    card_item_prefab: Prefab = null!

    gamePlayers: Node[] = []

    protected override initUI(): void {
        this.board.node.active = false
        cat.cardPool = new BasePool(this.card_item_prefab)
    }

    protected override onEventListener(): void {
        cat.event
            .on<SocketEvent>(
                'MT_GAME_INFO_BROADCAST',
                this.onBroadcastStateSocketHandler,
                this
            )
            .on<SocketEvent>(
                'MT_STATE_CHANGED_BROADCAST',
                this.onBroadcastStateSocketHandler,
                this
            )
            .on<SocketEvent>(
                'MT_PLAYER_INFO_MESSAGE',
                this.onPlayerInfoBroadcastHandler,
                this
            )
            .on<SocketEvent>(
                'MT_PLAYER_STATUS_CHANGED_BROADCAST',
                this.onPlayerStatusChangedHandler,
                this
            )
    }

    protected override onAutoObserver(): void {
        this.addReaction(
            () => store.game.playerList,
            (players) => {
                players.forEach((player) => {
                    const find = this.gamePlayers.find(
                        (item) =>
                            item.getComponent(PlayerItem)?.props.player
                                .index === player.index
                    )
                    if (find) {
                        find.getComponent(PlayerItem)?.setUpdateProps({
                            player,
                        })
                    }
                })
            }
        )
    }

    /**初始化-阶段 */
    private async onPhaseInit() {
        window.ccLog('UIGame-->onPhaseInit')
        cat.audio.playEffect(AudioEffectConstant.GAME_START)
        cat.tracking.game.roomEnter()

        this.initBoard()
        // 分配座次
        await this.initPlayersDynamic()
    }

    private async onBroadcastStateSocketHandler(data: GameInfoBroadcast) {
        const { stateInfo } = data

        window.ccLog('UIGame-->onBroadcastStateSocketHandler', stateInfo)
        // 新消息的时间戳小于当前时间戳则丢弃
        if (
            data.stateInfo?.serverTimestamp &&
            store.game.roomData?.stateInfo?.serverTimestamp &&
            data.stateInfo?.serverTimestamp <
                store.game.roomData?.stateInfo?.serverTimestamp
        ) {
            window.ccLog('UIGame-->onBroadcastStateSocketHandler 丢弃旧消息')
            return
        }

        store.game.roomData = data
        // 更新socket服务器时间
        if (data.stateInfo?.serverTimestamp) {
            const diff = Number(data.stateInfo?.serverTimestamp) - Date.now()
            store.global.ws_diff_server_timer = diff
            window.ccLog('ws时间差(ms)', diff)
        }

        if (stateInfo) {
            const { state } = stateInfo
        } else {
            window.ccLog('UIGame-->stateInfo为空，丢弃旧消息')
            return
        }

        switch (data.stateInfo?.state) {
            case DeskState.GAME_START:
                this.onPhaseInit()
                break
            default:
                break
        }
    }

    private onPlayerInfoBroadcastHandler(data: PlayerInfoMessage) {
        window.ccLog('UIGame-->onPlayerInfoBroadcastHandler', data)
        cat.gui.hideLoading()
        store.game.playerInfo = data
    }

    private onPlayerStatusChangedHandler(data: Player) {
        store.game.updatePlayerStatus(data)
    }

    async initPlayersDynamic() {}

    // 游戏已经启动，直接显示玩家头像
    initPlayerStatic() {}

    // 初始化棋盘和棋子
    private initBoard() {
        this.board.node.active = true
    }

    // 在游戏开局后进入游戏,会在 Game.ts 中调用当前方法
    // 需要初始化玩家和棋盘，以及棋子的位置
    syncGameInfo() {
        window.ccLog('UIGame-->syncGameInfo')
        this.initPlayerStatic()
        this.initBoard()
    }
}
