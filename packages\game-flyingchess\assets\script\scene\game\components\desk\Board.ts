import { _decorator, Component, Node, Vec3, UITransform } from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import store from '@/core/business/store'

const { ccclass, property } = _decorator

// 玩家位置枚举
export enum PlayerPosition {
    BLUE = 0, // 蓝色玩家
    RED = 1, // 红色玩家
    GREEN = 2, // 绿色玩家
    YELLOW = 3, // 黄色玩家
}

// 玩家旋转角度映射（按 PlayerPosition 索引顺序）
const PLAYER_ROTATION_ANGLES = [180, 90, 0, -90] as const

@ccclass('Board')
export class Board extends BaseComponent {
    private boardSize: number = 0
    private gridSize: number = 0

    protected override onLoad(): void {}

    protected override initUI(): void {
        window.ccLog('棋盘初始化...')
        this.initBorad()
    }

    private initBorad() {
        const currentPlayerPositon =
            store.game.currentPlayerPositon || PlayerPosition.BLUE

        // 根据玩家位置设置棋盘旋转角度
        const rotationAngle = PLAYER_ROTATION_ANGLES[currentPlayerPositon]
        window.ccLog('当前玩家', store.game.currentPlayer)
        window.ccLog('当前玩家位置', currentPlayerPositon)
        window.ccLog('当前玩家旋转角度', rotationAngle)
        // 设置棋盘旋转
        this.node.setRotationFromEuler(new Vec3(0, 0, rotationAngle))
    }

    override onDestroy(): void {}
}
